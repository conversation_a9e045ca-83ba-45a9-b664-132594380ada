<?php
/**
 * SchedSpot Dashboard Shortcode
 *
 * Handles the dashboard shortcode functionality
 *
 * @package SchedSpot
 * @version 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SchedSpot_Dashboard Class.
 *
 * @class SchedSpot_Dashboard
 * @version 1.0.0
 */
class SchedSpot_Dashboard {

    /**
     * Constructor.
     *
     * @since 1.0.0
     */
    public function __construct() {
        $this->init();
    }

    /**
     * Initialize dashboard functionality.
     *
     * @since 1.0.0
     */
    public function init() {
        add_shortcode( 'schedspot_dashboard', array( $this, 'render_dashboard' ) );
        add_action( 'wp_ajax_schedspot_update_worker_availability', array( $this, 'update_worker_availability' ) );
        add_action( 'wp_ajax_schedspot_get_dashboard_data', array( $this, 'get_dashboard_data' ) );
    }

    /**
     * Render dashboard shortcode.
     *
     * @since 1.0.0
     * @param array $atts Shortcode attributes.
     * @return string Dashboard HTML.
     */
    public function render_dashboard( $atts ) {
        if ( ! is_user_logged_in() ) {
            return $this->render_login_prompt();
        }

        $current_user = wp_get_current_user();
        $user_role = $this->get_user_primary_role( $current_user );

        $atts = shortcode_atts( array(
            'view' => 'auto', // auto, customer, worker
            'show_navigation' => 'true',
        ), $atts );

        // Determine view based on user role if auto
        if ( $atts['view'] === 'auto' ) {
            $atts['view'] = ( $user_role === 'schedspot_worker' ) ? 'worker' : 'customer';
        }

        ob_start();
        ?>
        <div id="schedspot-dashboard" class="schedspot-dashboard" data-user-role="<?php echo esc_attr( $user_role ); ?>">
            <?php if ( $atts['show_navigation'] === 'true' ) : ?>
                <?php $this->render_dashboard_navigation( $atts['view'] ); ?>
            <?php endif; ?>
            
            <div class="dashboard-content">
                <?php
                if ( $atts['view'] === 'worker' ) {
                    $this->render_worker_dashboard();
                } else {
                    $this->render_customer_dashboard();
                }
                ?>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Render login prompt for non-logged-in users.
     *
     * @since 1.0.0
     * @return string Login prompt HTML.
     */
    private function render_login_prompt() {
        ob_start();
        ?>
        <div class="schedspot-login-prompt">
            <div class="login-prompt-content">
                <h3><?php _e( 'Please Log In', 'schedspot' ); ?></h3>
                <p><?php _e( 'You need to be logged in to access your dashboard.', 'schedspot' ); ?></p>
                <div class="login-prompt-actions">
                    <a href="<?php echo wp_login_url( get_permalink() ); ?>" class="schedspot-btn schedspot-btn-primary">
                        <?php _e( 'Log In', 'schedspot' ); ?>
                    </a>
                    <a href="<?php echo wp_registration_url(); ?>" class="schedspot-btn schedspot-btn-secondary">
                        <?php _e( 'Register', 'schedspot' ); ?>
                    </a>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Render dashboard navigation.
     *
     * @since 1.0.0
     * @param string $view Current view (worker or customer).
     */
    private function render_dashboard_navigation( $view ) {
        ?>
        <div class="dashboard-navigation">
            <div class="dashboard-tabs">
                <button class="dashboard-tab active" data-tab="overview">
                    <span class="dashicons dashicons-dashboard"></span>
                    <?php _e( 'Overview', 'schedspot' ); ?>
                </button>
                
                <button class="dashboard-tab" data-tab="bookings">
                    <span class="dashicons dashicons-calendar-alt"></span>
                    <?php _e( 'Bookings', 'schedspot' ); ?>
                </button>
                
                <?php if ( $view === 'worker' ) : ?>
                    <button class="dashboard-tab" data-tab="earnings">
                        <span class="dashicons dashicons-money-alt"></span>
                        <?php _e( 'Earnings', 'schedspot' ); ?>
                    </button>
                    
                    <button class="dashboard-tab" data-tab="schedule">
                        <span class="dashicons dashicons-clock"></span>
                        <?php _e( 'Schedule', 'schedspot' ); ?>
                    </button>
                    
                    <button class="dashboard-tab" data-tab="services">
                        <span class="dashicons dashicons-admin-tools"></span>
                        <?php _e( 'Services', 'schedspot' ); ?>
                    </button>
                <?php endif; ?>
                
                <button class="dashboard-tab" data-tab="messages">
                    <span class="dashicons dashicons-email-alt"></span>
                    <?php _e( 'Messages', 'schedspot' ); ?>
                </button>
                
                <button class="dashboard-tab" data-tab="profile">
                    <span class="dashicons dashicons-admin-users"></span>
                    <?php _e( 'Profile', 'schedspot' ); ?>
                </button>
            </div>
        </div>
        <?php
    }

    /**
     * Render worker dashboard.
     *
     * @since 1.0.0
     */
    private function render_worker_dashboard() {
        $current_user = wp_get_current_user();
        $worker_id = $current_user->ID;
        $availability_status = get_user_meta( $worker_id, 'schedspot_worker_available', true );
        ?>
        <div class="worker-dashboard">
            <!-- Overview Tab -->
            <div id="overview" class="tab-content active">
                <div class="dashboard-header">
                    <h2><?php printf( __( 'Welcome back, %s!', 'schedspot' ), $current_user->display_name ); ?></h2>
                    
                    <div class="availability-toggle-container">
                        <div class="availability-status">
                            <span id="availability-status" class="status-indicator <?php echo $availability_status ? 'available' : 'unavailable'; ?>">
                                <?php echo $availability_status ? __( 'Available', 'schedspot' ) : __( 'Unavailable', 'schedspot' ); ?>
                            </span>
                        </div>
                        <button id="availability-toggle" class="availability-toggle <?php echo $availability_status ? 'btn-danger' : 'btn-success'; ?>">
                            <?php echo $availability_status ? __( 'Go Offline', 'schedspot' ) : __( 'Go Online', 'schedspot' ); ?>
                        </button>
                    </div>
                </div>

                <div class="dashboard-stats">
                    <?php $this->render_worker_stats( $worker_id ); ?>
                </div>

                <div class="dashboard-widgets">
                    <div class="widget">
                        <h3><?php _e( 'Today\'s Bookings', 'schedspot' ); ?></h3>
                        <?php $this->render_todays_bookings( $worker_id ); ?>
                    </div>
                    
                    <div class="widget">
                        <h3><?php _e( 'Recent Messages', 'schedspot' ); ?></h3>
                        <?php $this->render_recent_messages( $worker_id ); ?>
                    </div>
                </div>
            </div>

            <!-- Bookings Tab -->
            <div id="bookings" class="tab-content">
                <h3><?php _e( 'My Bookings', 'schedspot' ); ?></h3>
                <?php $this->render_worker_bookings( $worker_id ); ?>
            </div>

            <!-- Earnings Tab -->
            <div id="earnings" class="tab-content">
                <h3><?php _e( 'Earnings Overview', 'schedspot' ); ?></h3>
                <?php $this->render_worker_earnings( $worker_id ); ?>
            </div>

            <!-- Schedule Tab -->
            <div id="schedule" class="tab-content">
                <h3><?php _e( 'Manage Schedule', 'schedspot' ); ?></h3>
                <?php $this->render_worker_schedule( $worker_id ); ?>
            </div>

            <!-- Services Tab -->
            <div id="services" class="tab-content">
                <h3><?php _e( 'My Services', 'schedspot' ); ?></h3>
                <?php $this->render_worker_services( $worker_id ); ?>
            </div>

            <!-- Messages Tab -->
            <div id="messages" class="tab-content">
                <h3><?php _e( 'Messages', 'schedspot' ); ?></h3>
                <?php echo do_shortcode( '[schedspot_messages]' ); ?>
            </div>

            <!-- Profile Tab -->
            <div id="profile" class="tab-content">
                <h3><?php _e( 'Profile Settings', 'schedspot' ); ?></h3>
                <?php echo do_shortcode( '[schedspot_profile]' ); ?>
            </div>
        </div>
        <?php
    }

    /**
     * Render customer dashboard.
     *
     * @since 1.0.0
     */
    private function render_customer_dashboard() {
        $current_user = wp_get_current_user();
        $customer_id = $current_user->ID;
        ?>
        <div class="customer-dashboard">
            <!-- Overview Tab -->
            <div id="overview" class="tab-content active">
                <div class="dashboard-header">
                    <h2><?php printf( __( 'Welcome back, %s!', 'schedspot' ), $current_user->display_name ); ?></h2>
                    
                    <div class="quick-actions">
                        <a href="#" class="schedspot-btn schedspot-btn-primary book-service-btn">
                            <span class="dashicons dashicons-plus"></span>
                            <?php _e( 'Book a Service', 'schedspot' ); ?>
                        </a>
                    </div>
                </div>

                <div class="dashboard-stats">
                    <?php $this->render_customer_stats( $customer_id ); ?>
                </div>

                <div class="dashboard-widgets">
                    <div class="widget">
                        <h3><?php _e( 'Upcoming Bookings', 'schedspot' ); ?></h3>
                        <?php $this->render_upcoming_bookings( $customer_id ); ?>
                    </div>
                    
                    <div class="widget">
                        <h3><?php _e( 'Recent Messages', 'schedspot' ); ?></h3>
                        <?php $this->render_recent_messages( $customer_id ); ?>
                    </div>
                </div>
            </div>

            <!-- Bookings Tab -->
            <div id="bookings" class="tab-content">
                <h3><?php _e( 'My Bookings', 'schedspot' ); ?></h3>
                <?php $this->render_customer_bookings( $customer_id ); ?>
            </div>

            <!-- Messages Tab -->
            <div id="messages" class="tab-content">
                <h3><?php _e( 'Messages', 'schedspot' ); ?></h3>
                <?php echo do_shortcode( '[schedspot_messages]' ); ?>
            </div>

            <!-- Profile Tab -->
            <div id="profile" class="tab-content">
                <h3><?php _e( 'Profile Settings', 'schedspot' ); ?></h3>
                <?php echo do_shortcode( '[schedspot_profile]' ); ?>
            </div>
        </div>
        <?php
    }

    /**
     * Render worker statistics.
     *
     * @since 1.0.0
     * @param int $worker_id Worker ID.
     */
    private function render_worker_stats( $worker_id ) {
        // Get worker statistics
        $stats = array(
            'total_bookings' => $this->get_worker_total_bookings( $worker_id ),
            'this_month_earnings' => $this->get_worker_monthly_earnings( $worker_id ),
            'average_rating' => $this->get_worker_average_rating( $worker_id ),
            'completion_rate' => $this->get_worker_completion_rate( $worker_id ),
        );
        ?>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo esc_html( $stats['total_bookings'] ); ?></div>
                <div class="stat-label"><?php _e( 'Total Bookings', 'schedspot' ); ?></div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number">$<?php echo esc_html( number_format( $stats['this_month_earnings'], 2 ) ); ?></div>
                <div class="stat-label"><?php _e( 'This Month', 'schedspot' ); ?></div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?php echo esc_html( number_format( $stats['average_rating'], 1 ) ); ?></div>
                <div class="stat-label"><?php _e( 'Avg Rating', 'schedspot' ); ?></div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?php echo esc_html( number_format( $stats['completion_rate'], 1 ) ); ?>%</div>
                <div class="stat-label"><?php _e( 'Completion Rate', 'schedspot' ); ?></div>
            </div>
        </div>
        <?php
    }

    /**
     * Render customer statistics.
     *
     * @since 1.0.0
     * @param int $customer_id Customer ID.
     */
    private function render_customer_stats( $customer_id ) {
        // Get customer statistics
        $stats = array(
            'total_bookings' => $this->get_customer_total_bookings( $customer_id ),
            'total_spent' => $this->get_customer_total_spent( $customer_id ),
            'favorite_service' => $this->get_customer_favorite_service( $customer_id ),
            'member_since' => $this->get_customer_member_since( $customer_id ),
        );
        ?>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo esc_html( $stats['total_bookings'] ); ?></div>
                <div class="stat-label"><?php _e( 'Total Bookings', 'schedspot' ); ?></div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number">$<?php echo esc_html( number_format( $stats['total_spent'], 2 ) ); ?></div>
                <div class="stat-label"><?php _e( 'Total Spent', 'schedspot' ); ?></div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?php echo esc_html( $stats['favorite_service'] ); ?></div>
                <div class="stat-label"><?php _e( 'Favorite Service', 'schedspot' ); ?></div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?php echo esc_html( $stats['member_since'] ); ?></div>
                <div class="stat-label"><?php _e( 'Member Since', 'schedspot' ); ?></div>
            </div>
        </div>
        <?php
    }

    /**
     * Update worker availability status.
     *
     * @since 1.0.0
     */
    public function update_worker_availability() {
        if ( ! is_user_logged_in() ) {
            wp_send_json_error( array( 'message' => __( 'You must be logged in.', 'schedspot' ) ) );
        }

        $user_id = get_current_user_id();
        $available = isset( $_POST['available'] ) ? (bool) $_POST['available'] : false;

        update_user_meta( $user_id, 'schedspot_worker_available', $available );

        wp_send_json_success( array(
            'message' => $available ? __( 'You are now available for bookings.', 'schedspot' ) : __( 'You are now offline.', 'schedspot' ),
            'available' => $available
        ) );
    }

    /**
     * Get dashboard data via AJAX.
     *
     * @since 1.0.0
     */
    public function get_dashboard_data() {
        if ( ! is_user_logged_in() ) {
            wp_send_json_error( array( 'message' => __( 'You must be logged in.', 'schedspot' ) ) );
        }

        $user_id = get_current_user_id();
        $user_role = $this->get_user_primary_role( wp_get_current_user() );

        $data = array(
            'notifications_count' => $this->get_user_notifications_count( $user_id ),
            'recent_activity' => $this->get_user_recent_activity( $user_id ),
        );

        if ( $user_role === 'schedspot_worker' ) {
            $data['stats'] = array(
                'total_bookings' => $this->get_worker_total_bookings( $user_id ),
                'this_month_earnings' => $this->get_worker_monthly_earnings( $user_id ),
                'average_rating' => $this->get_worker_average_rating( $user_id ),
                'completion_rate' => $this->get_worker_completion_rate( $user_id ),
            );
        } else {
            $data['stats'] = array(
                'total_bookings' => $this->get_customer_total_bookings( $user_id ),
                'total_spent' => $this->get_customer_total_spent( $user_id ),
            );
        }

        wp_send_json_success( $data );
    }

    /**
     * Get user's primary role.
     *
     * @since 1.0.0
     * @param WP_User $user User object.
     * @return string Primary role.
     */
    private function get_user_primary_role( $user ) {
        if ( empty( $user->roles ) ) {
            return 'subscriber';
        }
        
        // Check for SchedSpot roles first
        if ( in_array( 'schedspot_worker', $user->roles ) ) {
            return 'schedspot_worker';
        }
        
        return $user->roles[0];
    }

    // Placeholder methods for statistics - these would be implemented with actual database queries
    private function get_worker_total_bookings( $worker_id ) { return 42; }
    private function get_worker_monthly_earnings( $worker_id ) { return 1250.00; }
    private function get_worker_average_rating( $worker_id ) { return 4.8; }
    private function get_worker_completion_rate( $worker_id ) { return 95.5; }
    private function get_customer_total_bookings( $customer_id ) { return 8; }
    private function get_customer_total_spent( $customer_id ) { return 450.00; }
    private function get_customer_favorite_service( $customer_id ) { return 'House Cleaning'; }
    private function get_customer_member_since( $customer_id ) { return '2024'; }
    private function get_user_notifications_count( $user_id ) { return 3; }
    private function get_user_recent_activity( $user_id ) { return array(); }

    // Placeholder render methods - these would contain actual implementation
    private function render_todays_bookings( $worker_id ) { echo '<p>Today\'s bookings will be displayed here.</p>'; }
    private function render_recent_messages( $user_id ) { echo '<p>Recent messages will be displayed here.</p>'; }
    private function render_worker_bookings( $worker_id ) { echo '<p>Worker bookings list will be displayed here.</p>'; }
    private function render_worker_earnings( $worker_id ) { echo '<p>Worker earnings details will be displayed here.</p>'; }
    private function render_worker_schedule( $worker_id ) { echo '<p>Worker schedule management will be displayed here.</p>'; }
    private function render_worker_services( $worker_id ) { echo '<p>Worker services management will be displayed here.</p>'; }
    private function render_upcoming_bookings( $customer_id ) { echo '<p>Upcoming bookings will be displayed here.</p>'; }
    private function render_customer_bookings( $customer_id ) { echo '<p>Customer bookings list will be displayed here.</p>'; }
}
