<?php
/**
 * SchedSpot Messages Shortcode
 *
 * Handles the messaging interface shortcode functionality
 *
 * @package SchedSpot
 * @version 1.6.1
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SchedSpot_Messages Class.
 *
 * @class SchedSpot_Messages
 * @version 1.6.1
 */
class SchedSpot_Messages {

    /**
     * Constructor.
     *
     * @since 1.6.1
     */
    public function __construct() {
        $this->init();
    }

    /**
     * Initialize messages functionality.
     *
     * @since 1.6.1
     */
    public function init() {
        add_shortcode( 'schedspot_messages', array( $this, 'render_messages' ) );
        add_action( 'wp_ajax_schedspot_get_conversations', array( $this, 'get_conversations' ) );
        add_action( 'wp_ajax_schedspot_get_conversation_messages', array( $this, 'get_conversation_messages' ) );
        add_action( 'wp_ajax_schedspot_send_message', array( $this, 'send_message' ) );
        add_action( 'wp_ajax_schedspot_mark_messages_read', array( $this, 'mark_messages_read' ) );
    }

    /**
     * Render messages shortcode.
     *
     * @since 1.6.1
     * @param array $atts Shortcode attributes.
     * @return string Messages interface HTML.
     */
    public function render_messages( $atts ) {
        if ( ! is_user_logged_in() ) {
            return $this->render_login_prompt();
        }

        $atts = shortcode_atts( array(
            'conversation_id' => '',
            'show_conversation_list' => 'true',
            'height' => '600px',
        ), $atts );

        ob_start();
        ?>
        <div class="schedspot-messaging" style="height: <?php echo esc_attr( $atts['height'] ); ?>;">
            <?php if ( $atts['show_conversation_list'] === 'true' ) : ?>
                <div class="conversations-list">
                    <div class="conversations-header">
                        <h3><?php _e( 'Messages', 'schedspot' ); ?></h3>
                        <button class="new-conversation-btn" title="<?php _e( 'Start New Conversation', 'schedspot' ); ?>">
                            <span class="dashicons dashicons-plus"></span>
                        </button>
                    </div>
                    <div class="conversations-container">
                        <div class="conversations-loading">
                            <span class="dashicons dashicons-update spin"></span>
                            <?php _e( 'Loading conversations...', 'schedspot' ); ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <div class="chat-area <?php echo $atts['conversation_id'] ? 'has-conversation' : 'no-conversation'; ?>">
                <?php if ( $atts['conversation_id'] ) : ?>
                    <?php $this->render_conversation( $atts['conversation_id'] ); ?>
                <?php else : ?>
                    <div class="no-conversation-selected">
                        <div class="no-conversation-content">
                            <span class="dashicons dashicons-email-alt"></span>
                            <h3><?php _e( 'Select a Conversation', 'schedspot' ); ?></h3>
                            <p><?php _e( 'Choose a conversation from the list to start messaging.', 'schedspot' ); ?></p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- New Conversation Modal -->
        <div id="new-conversation-modal" class="schedspot-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><?php _e( 'Start New Conversation', 'schedspot' ); ?></h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="new-conversation-form">
                        <div class="form-row">
                            <label for="recipient-search"><?php _e( 'Send message to:', 'schedspot' ); ?></label>
                            <input type="text" id="recipient-search" placeholder="<?php _e( 'Search for users...', 'schedspot' ); ?>">
                            <div id="recipient-suggestions"></div>
                        </div>
                        <div class="form-row">
                            <label for="initial-message"><?php _e( 'Message:', 'schedspot' ); ?></label>
                            <textarea id="initial-message" rows="4" placeholder="<?php _e( 'Type your message...', 'schedspot' ); ?>" required></textarea>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="button cancel-btn"><?php _e( 'Cancel', 'schedspot' ); ?></button>
                            <button type="submit" class="button button-primary"><?php _e( 'Send Message', 'schedspot' ); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Render login prompt for non-logged-in users.
     *
     * @since 1.6.1
     * @return string Login prompt HTML.
     */
    private function render_login_prompt() {
        ob_start();
        ?>
        <div class="schedspot-login-prompt">
            <div class="login-prompt-content">
                <span class="dashicons dashicons-email-alt"></span>
                <h3><?php _e( 'Please Log In', 'schedspot' ); ?></h3>
                <p><?php _e( 'You need to be logged in to access your messages.', 'schedspot' ); ?></p>
                <div class="login-prompt-actions">
                    <a href="<?php echo wp_login_url( get_permalink() ); ?>" class="schedspot-btn schedspot-btn-primary">
                        <?php _e( 'Log In', 'schedspot' ); ?>
                    </a>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Render specific conversation.
     *
     * @since 1.6.1
     * @param int $conversation_id Conversation ID.
     */
    private function render_conversation( $conversation_id ) {
        $conversation = $this->get_conversation_details( $conversation_id );
        
        if ( ! $conversation ) {
            echo '<div class="conversation-error">' . __( 'Conversation not found.', 'schedspot' ) . '</div>';
            return;
        }
        ?>
        <div class="chat-header">
            <div class="chat-user-info">
                <img src="<?php echo esc_url( $conversation['user_avatar'] ); ?>" alt="<?php echo esc_attr( $conversation['user_name'] ); ?>" class="chat-user-avatar">
                <div class="chat-user-details">
                    <div class="chat-user-name"><?php echo esc_html( $conversation['user_name'] ); ?></div>
                    <div class="chat-user-status"><?php echo esc_html( $conversation['user_status'] ); ?></div>
                </div>
            </div>
            <div class="chat-actions">
                <button class="chat-action-btn" title="<?php _e( 'Call', 'schedspot' ); ?>">
                    <span class="dashicons dashicons-phone"></span>
                </button>
                <button class="chat-action-btn" title="<?php _e( 'Video Call', 'schedspot' ); ?>">
                    <span class="dashicons dashicons-video-alt3"></span>
                </button>
                <button class="chat-action-btn" title="<?php _e( 'More Options', 'schedspot' ); ?>">
                    <span class="dashicons dashicons-ellipsis"></span>
                </button>
            </div>
        </div>

        <div class="messages-container" data-conversation-id="<?php echo esc_attr( $conversation_id ); ?>">
            <div class="messages-loading">
                <span class="dashicons dashicons-update spin"></span>
                <?php _e( 'Loading messages...', 'schedspot' ); ?>
            </div>
        </div>

        <div class="message-input-area">
            <div class="file-upload-area">
                <div class="file-upload-text">
                    <span class="dashicons dashicons-paperclip"></span>
                    <?php _e( 'Drop files here or click to upload', 'schedspot' ); ?>
                </div>
                <input type="file" class="file-input" multiple accept="image/*,.pdf,.doc,.docx,.txt">
            </div>
            
            <div class="message-input-container">
                <textarea class="message-input" placeholder="<?php _e( 'Type your message...', 'schedspot' ); ?>" rows="1"></textarea>
                <div class="message-actions">
                    <button class="message-action-btn attach-file-btn" title="<?php _e( 'Attach File', 'schedspot' ); ?>">
                        <span class="dashicons dashicons-paperclip"></span>
                    </button>
                    <button class="message-action-btn emoji-btn" title="<?php _e( 'Add Emoji', 'schedspot' ); ?>">
                        <span class="dashicons dashicons-smiley"></span>
                    </button>
                    <button class="message-action-btn send-message-btn" title="<?php _e( 'Send Message', 'schedspot' ); ?>">
                        <span class="dashicons dashicons-arrow-right-alt2"></span>
                    </button>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Get conversations via AJAX.
     *
     * @since 1.6.1
     */
    public function get_conversations() {
        if ( ! is_user_logged_in() ) {
            wp_send_json_error( array( 'message' => __( 'You must be logged in.', 'schedspot' ) ) );
        }

        $user_id = get_current_user_id();
        $conversations = $this->fetch_user_conversations( $user_id );

        wp_send_json_success( array(
            'conversations' => $conversations,
            'total' => count( $conversations )
        ) );
    }

    /**
     * Get conversation messages via AJAX.
     *
     * @since 1.6.1
     */
    public function get_conversation_messages() {
        if ( ! is_user_logged_in() ) {
            wp_send_json_error( array( 'message' => __( 'You must be logged in.', 'schedspot' ) ) );
        }

        $conversation_id = absint( $_GET['conversation_id'] );
        $page = absint( $_GET['page'] ) ?: 1;
        $per_page = 50;

        if ( ! $conversation_id ) {
            wp_send_json_error( array( 'message' => __( 'Invalid conversation ID.', 'schedspot' ) ) );
        }

        $messages = $this->fetch_conversation_messages( $conversation_id, $page, $per_page );
        $conversation = $this->get_conversation_details( $conversation_id );

        wp_send_json_success( array(
            'messages' => $messages,
            'conversation' => $conversation,
            'has_more' => count( $messages ) === $per_page
        ) );
    }

    /**
     * Send message via AJAX.
     *
     * @since 1.6.1
     */
    public function send_message() {
        if ( ! is_user_logged_in() ) {
            wp_send_json_error( array( 'message' => __( 'You must be logged in.', 'schedspot' ) ) );
        }

        $sender_id = get_current_user_id();
        $recipient_id = absint( $_POST['recipient_id'] );
        $content = sanitize_textarea_field( $_POST['content'] );
        $conversation_id = absint( $_POST['conversation_id'] );

        if ( ! $recipient_id || ! $content ) {
            wp_send_json_error( array( 'message' => __( 'Missing required fields.', 'schedspot' ) ) );
        }

        // Handle file attachments
        $attachments = array();
        if ( ! empty( $_FILES['attachments'] ) ) {
            $attachments = $this->handle_file_uploads( $_FILES['attachments'] );
        }

        $message_data = array(
            'sender_id' => $sender_id,
            'recipient_id' => $recipient_id,
            'conversation_id' => $conversation_id,
            'content' => $content,
            'attachments' => $attachments,
            'created_at' => current_time( 'mysql' ),
        );

        $message_id = $this->save_message( $message_data );

        if ( $message_id ) {
            // Send notification
            $this->send_message_notification( $message_id );

            wp_send_json_success( array(
                'message' => __( 'Message sent successfully.', 'schedspot' ),
                'message_id' => $message_id,
                'message_data' => $this->format_message_for_display( $message_data )
            ) );
        } else {
            wp_send_json_error( array( 'message' => __( 'Failed to send message.', 'schedspot' ) ) );
        }
    }

    /**
     * Mark messages as read via AJAX.
     *
     * @since 1.6.1
     */
    public function mark_messages_read() {
        if ( ! is_user_logged_in() ) {
            wp_send_json_error( array( 'message' => __( 'You must be logged in.', 'schedspot' ) ) );
        }

        $conversation_id = absint( $_POST['conversation_id'] );
        $user_id = get_current_user_id();

        if ( ! $conversation_id ) {
            wp_send_json_error( array( 'message' => __( 'Invalid conversation ID.', 'schedspot' ) ) );
        }

        $marked = $this->mark_conversation_messages_read( $conversation_id, $user_id );

        if ( $marked ) {
            wp_send_json_success( array( 'message' => __( 'Messages marked as read.', 'schedspot' ) ) );
        } else {
            wp_send_json_error( array( 'message' => __( 'Failed to mark messages as read.', 'schedspot' ) ) );
        }
    }

    /**
     * Handle file uploads for message attachments.
     *
     * @since 1.6.1
     * @param array $files Uploaded files array.
     * @return array Processed attachments.
     */
    private function handle_file_uploads( $files ) {
        $attachments = array();
        $allowed_types = array( 'image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'text/plain' );
        $max_file_size = 5 * 1024 * 1024; // 5MB

        if ( ! is_array( $files['name'] ) ) {
            $files = array(
                'name' => array( $files['name'] ),
                'type' => array( $files['type'] ),
                'tmp_name' => array( $files['tmp_name'] ),
                'error' => array( $files['error'] ),
                'size' => array( $files['size'] ),
            );
        }

        for ( $i = 0; $i < count( $files['name'] ); $i++ ) {
            if ( $files['error'][ $i ] !== UPLOAD_ERR_OK ) {
                continue;
            }

            if ( ! in_array( $files['type'][ $i ], $allowed_types ) ) {
                continue;
            }

            if ( $files['size'][ $i ] > $max_file_size ) {
                continue;
            }

            $upload = wp_handle_upload( array(
                'name' => $files['name'][ $i ],
                'type' => $files['type'][ $i ],
                'tmp_name' => $files['tmp_name'][ $i ],
                'error' => $files['error'][ $i ],
                'size' => $files['size'][ $i ],
            ), array( 'test_form' => false ) );

            if ( ! isset( $upload['error'] ) ) {
                $attachments[] = array(
                    'name' => $files['name'][ $i ],
                    'url' => $upload['url'],
                    'type' => $files['type'][ $i ],
                    'size' => $files['size'][ $i ],
                );
            }
        }

        return $attachments;
    }

    // Placeholder methods for database operations - these would be implemented with actual database queries
    private function fetch_user_conversations( $user_id ) {
        // This would fetch conversations from database
        return array();
    }

    private function fetch_conversation_messages( $conversation_id, $page, $per_page ) {
        // This would fetch messages from database
        return array();
    }

    private function get_conversation_details( $conversation_id ) {
        // This would fetch conversation details from database
        return array(
            'user_name' => 'Sample User',
            'user_avatar' => get_avatar_url( 0 ),
            'user_status' => 'Online',
        );
    }

    private function save_message( $message_data ) {
        // This would save message to database
        return 1;
    }

    private function send_message_notification( $message_id ) {
        // This would send notification to recipient
        return true;
    }

    private function format_message_for_display( $message_data ) {
        // This would format message for frontend display
        return $message_data;
    }

    private function mark_conversation_messages_read( $conversation_id, $user_id ) {
        // This would mark messages as read in database
        return true;
    }
}
